<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>747</width>
    <height>486</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <widget class="QComboBox" name="ComboDevices">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>20</y>
      <width>511</width>
      <height>22</height>
     </rect>
    </property>
   </widget>
   <widget class="QWidget" name="widgetDisplay" native="true">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>60</y>
      <width>511</width>
      <height>401</height>
     </rect>
    </property>
   </widget>
   <widget class="QGroupBox" name="groupInit">
    <property name="geometry">
     <rect>
      <x>530</x>
      <y>20</y>
      <width>211</width>
      <height>101</height>
     </rect>
    </property>
    <property name="title">
     <string>初始化</string>
    </property>
    <widget class="QWidget" name="gridLayoutWidget">
     <property name="geometry">
      <rect>
       <x>9</x>
       <y>19</y>
       <width>201</width>
       <height>81</height>
      </rect>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="2" column="2">
       <widget class="QPushButton" name="bnClose">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="text">
         <string>关闭设备</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QPushButton" name="bnOpen">
        <property name="text">
         <string>打开设备</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1" colspan="2">
       <widget class="QPushButton" name="bnEnum">
        <property name="text">
         <string>查找设备</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </widget>
   <widget class="QGroupBox" name="groupGrab">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>530</x>
      <y>130</y>
      <width>211</width>
      <height>171</height>
     </rect>
    </property>
    <property name="title">
     <string>采集</string>
    </property>
    <widget class="QWidget" name="gridLayoutWidget_2">
     <property name="geometry">
      <rect>
       <x>9</x>
       <y>19</y>
       <width>202</width>
       <height>141</height>
      </rect>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="4" column="0" colspan="2">
       <widget class="QPushButton" name="bnSaveImage">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="text">
         <string>保存图像</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QRadioButton" name="radioContinueMode">
        <property name="text">
         <string>连续模式</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QRadioButton" name="radioTriggerMode">
        <property name="text">
         <string>触发模式</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QPushButton" name="bnStop">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="text">
         <string>停止采集</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QPushButton" name="bnStart">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="text">
         <string>开始采集</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0" colspan="2">
       <widget class="QPushButton" name="bnSoftwareTrigger">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="text">
         <string>软触发一次</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </widget>
   <widget class="QGroupBox" name="groupParam">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>530</x>
      <y>310</y>
      <width>211</width>
      <height>151</height>
     </rect>
    </property>
    <property name="title">
     <string>参数</string>
    </property>
    <widget class="QWidget" name="gridLayoutWidget_3">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>20</y>
       <width>201</width>
       <height>131</height>
      </rect>
     </property>
     <layout class="QGridLayout" name="gridLayoutParam" columnstretch="2,3">
      <item row="3" column="0">
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>帧率</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="edtGain">
        <property name="text">
         <string>0</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>增益</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>曝光</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="edtExposureTime">
        <property name="text">
         <string>0</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QPushButton" name="bnGetParam">
        <property name="text">
         <string>获取参数</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QPushButton" name="bnSetParam">
        <property name="text">
         <string>设置参数</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QLineEdit" name="edtFrameRate">
        <property name="text">
         <string>0</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </widget>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <tabstops>
  <tabstop>ComboDevices</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
