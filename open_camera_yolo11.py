import sys
import json
import queue
import logging
import threading
import time
from ctypes import *
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, asdict
from typing import Optional, Tu<PERSON>
from ws_server import ResultBroadcaster

import cv2
import numpy as np
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
import socket

# 导入相机控制模块
sys.path.append("../MvImport")
from MvImport.MvCameraControl_class import *
from MvImport.CameraParams_header import *

# 导入 YOLOv11 模型
from ultralytics import YOLO

# 定义一些可能未定义的常量
MV_E_TRIGGER_TIMEOUT = 0x80040001

@dataclass
class CameraConfig:
    """相机配置类"""
    confidence_threshold: float = 0.5
    max_queue_size: int = 5
    model_path: str = "weights/yolo11n.pt"
    server_ip: str = "127.0.0.1"
    server_port: int = 8868
    yolo_input_size: int = 416
    max_fps: int = 30
    enable_gpu: bool = True
    log_level: str = "INFO"
    max_consecutive_errors: int = 5
    gc_interval: int = 100
    
    @classmethod
    def load_from_file(cls, filepath="config.json"):
        """从文件加载配置"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return cls(**data)
        except FileNotFoundError:
            logging.info("配置文件不存在，使用默认配置")
            return cls()
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
            return cls()
    
    def save_to_file(self, filepath="config.json"):
        """保存配置到文件"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(asdict(self), f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")

class PerformanceMonitor:
    """性能监控类"""
    def __init__(self):
        self.fps_counter = 0
        self.last_fps_time = time.time()
        self.current_fps = 0
        self.detection_times = []
        self.max_detection_history = 100
    
    def update_fps(self):
        """更新FPS计算"""
        self.fps_counter += 1
        current_time = time.time()
        if current_time - self.last_fps_time >= 1.0:
            self.current_fps = self.fps_counter
            self.fps_counter = 0
            self.last_fps_time = current_time
    
    def add_detection_time(self, detection_time: float):
        """添加检测耗时"""
        self.detection_times.append(detection_time)
        if len(self.detection_times) > self.max_detection_history:
            self.detection_times.pop(0)
    
    def get_avg_detection_time(self) -> float:
        """获取平均检测耗时"""
        return np.mean(self.detection_times) if self.detection_times else 0.0

class CameraWindow(QMainWindow):
    # 定义信号
    update_image_signal = pyqtSignal(np.ndarray)
    update_status_signal = pyqtSignal(str)
    show_error_signal = pyqtSignal(str, str)
    update_performance_signal = pyqtSignal(float, float)

    def __init__(self):
        super().__init__()
        
        # 加载配置
        self.config = CameraConfig.load_from_file()
        
        # 设置日志
        self.setup_logging()
        
        # 初始化变量
        self.cam = None
        self.running = False
        self.server_running = False
        self.trigger_mode = "Off"
        self.trigger_count = 0
        self.current_image = None
        self.frame_count = 0
        
        # 性能监控
        self.performance_monitor = PerformanceMonitor()
        
        # 线程和队列
        self.image_queue = queue.Queue(maxsize=self.config.max_queue_size)
        self.detection_executor = ThreadPoolExecutor(max_workers=2)
        self.capture_thread = None
        self.processing_thread = None
        self.server_thread = None
        
        # UI设置
        self.setWindowTitle("千歌机器人视觉相机采集系统 - 优化版")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建UI
        self.create_ui()
        
        # 连接信号
        self.connect_signals()
        
        # 初始化相机和模型
        self.init_camera()
        self.init_yolo_model()
        
        logging.info("应用程序初始化完成")

    def setup_logging(self):
        """设置日志系统"""
        log_level = getattr(logging, self.config.log_level.upper(), logging.INFO)
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('camera_app.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

    def connect_signals(self):
        """连接信号到槽函数"""
        self.update_image_signal.connect(self.display_image)
        self.update_status_signal.connect(self.update_status_bar)
        self.show_error_signal.connect(self.show_error_message)
        self.update_performance_signal.connect(self.update_performance_display)

    def create_ui(self):
        """创建用户界面"""
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QHBoxLayout(main_widget)
        
        # 左侧图像显示区域
        left_layout = QVBoxLayout()
        
        # 图像显示
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("background-color: black; border: 2px solid gray;")
        self.image_label.setMinimumSize(640, 480)
        left_layout.addWidget(self.image_label, stretch=1)
        
        # 性能显示
        performance_group = QGroupBox("性能监控")
        performance_layout = QHBoxLayout()
        
        self.fps_label = QLabel("FPS: 0")
        self.detection_time_label = QLabel("检测耗时: 0ms")
        self.queue_size_label = QLabel("队列: 0/5")
        
        performance_layout.addWidget(self.fps_label)
        performance_layout.addWidget(self.detection_time_label)
        performance_layout.addWidget(self.queue_size_label)
        performance_group.setLayout(performance_layout)
        left_layout.addWidget(performance_group)
        
        layout.addLayout(left_layout, stretch=2)
        
        # 右侧控制面板
        right_layout = QVBoxLayout()
        
        # 触发模式控制
        trigger_group = QGroupBox("触发模式")
        trigger_layout = QVBoxLayout()
        
        self.trigger_off = QRadioButton("连续采集")
        self.trigger_off.setChecked(True)
        self.trigger_off.toggled.connect(lambda: self.set_trigger_mode("Off"))
        
        self.trigger_software = QRadioButton("软件触发")
        self.trigger_software.toggled.connect(lambda: self.set_trigger_mode("Software"))
        
        self.trigger_hardware = QRadioButton("硬件触发")
        self.trigger_hardware.toggled.connect(lambda: self.set_trigger_mode("Hardware"))
        
        self.soft_trigger_btn = QPushButton("触发采集")
        self.soft_trigger_btn.clicked.connect(self.software_trigger)
        self.soft_trigger_btn.setEnabled(False)
        
        trigger_layout.addWidget(self.trigger_off)
        trigger_layout.addWidget(self.trigger_software)
        trigger_layout.addWidget(self.trigger_hardware)
        trigger_layout.addWidget(self.soft_trigger_btn)
        trigger_group.setLayout(trigger_layout)
        right_layout.addWidget(trigger_group)
        
        # 采集控制
        control_group = QGroupBox("采集控制")
        control_layout = QVBoxLayout()
        
        self.start_btn = QPushButton("开始采集")
        self.start_btn.clicked.connect(self.start_capture)
        
        self.stop_btn = QPushButton("停止采集")
        self.stop_btn.clicked.connect(self.stop_capture)
        self.stop_btn.setEnabled(False)
        
        self.save_btn = QPushButton("保存图像")
        self.save_btn.clicked.connect(self.save_image)
        self.save_btn.setEnabled(False)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.save_btn)
        control_group.setLayout(control_layout)
        right_layout.addWidget(control_group)
        
        # 检测参数设置
        detection_group = QGroupBox("检测参数")
        detection_layout = QFormLayout()
        
        self.confidence_slider = QSlider(Qt.Horizontal)
        self.confidence_slider.setRange(1, 100)
        self.confidence_slider.setValue(int(self.config.confidence_threshold * 100))
        self.confidence_slider.valueChanged.connect(self.update_confidence)
        
        self.confidence_label = QLabel(f"{self.config.confidence_threshold:.2f}")
        
        detection_layout.addRow("置信度:", self.confidence_slider)
        detection_layout.addRow("当前值:", self.confidence_label)
        detection_group.setLayout(detection_layout)
        right_layout.addWidget(detection_group)
        
        # 服务器控制
        server_group = QGroupBox("网络服务器")
        server_layout = QVBoxLayout()
        
        self.server_btn = QPushButton("启动服务器")
        self.server_btn.clicked.connect(self.toggle_server)
        
        self.server_status_label = QLabel("服务器状态: 停止")
        
        server_layout.addWidget(self.server_btn)
        server_layout.addWidget(self.server_status_label)
        server_group.setLayout(server_layout)
        right_layout.addWidget(server_group)
        
        # 配置保存
        config_group = QGroupBox("配置管理")
        config_layout = QVBoxLayout()
        
        save_config_btn = QPushButton("保存配置")
        save_config_btn.clicked.connect(self.save_config)
        
        load_config_btn = QPushButton("重载配置")
        load_config_btn.clicked.connect(self.load_config)
        
        config_layout.addWidget(save_config_btn)
        config_layout.addWidget(load_config_btn)
        config_group.setLayout(config_layout)
        right_layout.addWidget(config_group)
        
        right_layout.addStretch()
        layout.addLayout(right_layout, stretch=1)
        
        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("准备就绪")

    def init_camera(self):
        """初始化相机"""
        try:
            ret = MvCamera.MV_CC_Initialize()
            if ret != 0:
                raise Exception(f"初始化SDK失败! ret[0x{ret:x}]")

            deviceList = MV_CC_DEVICE_INFO_LIST()
            tlayerType = MV_GIGE_DEVICE | MV_USB_DEVICE
            ret = MvCamera.MV_CC_EnumDevices(tlayerType, deviceList)
            if ret != 0:
                raise Exception(f"枚举设备失败! ret[0x{ret:x}]")
            if deviceList.nDeviceNum < 1:
                raise Exception("未找到设备!")

            self.cam = MvCamera()
            stDeviceList = cast(deviceList.pDeviceInfo[0], POINTER(MV_CC_DEVICE_INFO)).contents
            ret = self.cam.MV_CC_CreateHandle(stDeviceList)
            if ret != 0:
                raise Exception(f"创建句柄失败! ret[0x{ret:x}]")

            ret = self.cam.MV_CC_OpenDevice(MV_ACCESS_Exclusive, 0)
            if ret != 0:
                raise Exception(f"打开设备失败! ret[0x{ret:x}]")

            ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
            if ret != 0:
                logging.warning(f"设置触发模式失败! ret[0x{ret:x}]")

            self.update_status_signal.emit("相机初始化成功")
            logging.info("相机初始化成功")
            
        except Exception as e:
            error_msg = f"初始化相机失败: {str(e)}"
            logging.error(error_msg)
            self.show_error_signal.emit("错误", error_msg)

    def init_yolo_model(self):
        """初始化YOLO模型"""
        try:
            self.model = YOLO(self.config.model_path)
            
            # 优化模型设置
            self.model.overrides['imgsz'] = self.config.yolo_input_size
            
            # 如果启用GPU且可用，使用半精度
            if self.config.enable_gpu:
                import torch
                if torch.cuda.is_available():
                    self.model.half()
                    logging.info("启用GPU半精度推理")
            
            # 模型预热
            dummy_image = np.zeros((480, 640, 3), dtype=np.uint8)
            for _ in range(3):
                self.model.predict(dummy_image, verbose=False)
            
            logging.info("YOLO模型初始化并预热完成")
            
        except Exception as e:
            error_msg = f"初始化YOLO模型失败: {str(e)}"
            logging.error(error_msg)
            self.show_error_signal.emit("错误", error_msg)

    def update_confidence(self, value):
        """更新置信度阈值"""
        self.config.confidence_threshold = value / 100.0
        self.confidence_label.setText(f"{self.config.confidence_threshold:.2f}")

    def set_trigger_mode(self, mode):
        """设置触发模式"""
        if self.cam is None:
            return

        self.trigger_mode = mode

        try:
            if mode == "Off":
                ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_OFF)
                self.soft_trigger_btn.setEnabled(False)
                self.update_status_signal.emit("触发模式: 连续采集")
            elif mode == "Software":
                ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_ON)
                ret = self.cam.MV_CC_SetEnumValue("TriggerSource", MV_TRIGGER_SOURCE_SOFTWARE)
                self.soft_trigger_btn.setEnabled(True)
                self.update_status_signal.emit("触发模式: 软件触发")
            elif mode == "Hardware":
                ret = self.cam.MV_CC_SetEnumValue("TriggerMode", MV_TRIGGER_MODE_ON)
                ret = self.cam.MV_CC_SetEnumValue("TriggerSource", MV_TRIGGER_SOURCE_LINE0)
                self.soft_trigger_btn.setEnabled(False)
                self.update_status_signal.emit("触发模式: 硬件触发")

            if ret != 0:
                logging.warning(f"设置触发模式失败! 错误码: 0x{ret:x}")
                
        except Exception as e:
            error_msg = f"设置触发模式时出错: {str(e)}"
            logging.error(error_msg)
            self.show_error_signal.emit("错误", error_msg)

    def software_trigger(self):
        """软件触发"""
        if self.cam is None or self.trigger_mode != "Software":
            return

        try:
            ret = self.cam.MV_CC_SetCommandValue("TriggerSoftware")
            if ret == 0:
                self.trigger_count += 1
                self.update_status_signal.emit(f"软件触发已发送 (总计: {self.trigger_count})")
                logging.debug(f"软件触发成功，总计: {self.trigger_count}")
            else:
                logging.warning(f"软件触发失败! 错误码: 0x{ret:x}")
                
        except Exception as e:
            error_msg = f"执行软件触发时出错: {str(e)}"
            logging.error(error_msg)
            self.show_error_signal.emit("错误", error_msg)

    def start_capture(self):
        """开始采集"""
        if self.cam is None:
            self.show_error_signal.emit("警告", "相机未初始化!")
            return

        try:
            ret = self.cam.MV_CC_StartGrabbing()
            if ret != 0:
                raise Exception(f"开始采集失败! ret[0x{ret:x}]")

            self.running = True
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.save_btn.setEnabled(True)
            
            # 启动采集和处理线程
            self.capture_thread = threading.Thread(target=self.capture_frame)
            self.processing_thread = threading.Thread(target=self.process_images)
            self.capture_thread.daemon = True
            self.processing_thread.daemon = True
            self.capture_thread.start()
            self.processing_thread.start()
            
            self.update_status_signal.emit("正在采集...")
            logging.info("开始图像采集")
            
        except Exception as e:
            error_msg = f"开始采集失败: {str(e)}"
            logging.error(error_msg)
            self.show_error_signal.emit("错误", error_msg)

    def stop_capture(self):
        """停止采集"""
        self.running = False
        
        # 等待线程结束
        if hasattr(self, 'capture_thread') and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=2.0)
        if hasattr(self, 'processing_thread') and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=2.0)

        try:
            if self.cam is not None:
                ret = self.cam.MV_CC_StopGrabbing()
                if ret != 0:
                    logging.warning(f"停止采集失败! ret[0x{ret:x}]")

            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.update_status_signal.emit("采集已停止")
            logging.info("停止图像采集")
            
        except Exception as e:
            logging.error(f"停止采集时出错: {str(e)}")

    def capture_frame(self):
        """图像采集线程 - 优化版"""
        stOutFrame = MV_FRAME_OUT()
        memset(byref(stOutFrame), 0, sizeof(stOutFrame))
        consecutive_errors = 0
        
        logging.info("采集线程启动")
        
        while self.running:
            try:
                ret = self.cam.MV_CC_GetImageBuffer(stOutFrame, 1000)
                if ret != 0:
                    if ret == MV_E_TRIGGER_TIMEOUT:
                        continue
                    consecutive_errors += 1
                    if consecutive_errors >= self.config.max_consecutive_errors:
                        self.handle_camera_error("连续采集错误过多")
                        break
                    continue

                consecutive_errors = 0  # 重置错误计数

                if stOutFrame.pBufAddr is None or stOutFrame.stFrameInfo.nFrameLen == 0:
                    logging.warning("图像缓冲区为空或帧长度为0!")
                    self.cam.MV_CC_FreeImageBuffer(stOutFrame)
                    continue

                # 转换图像数据
                image = self.convert_image_data(stOutFrame)
                if image is not None:
                    try:
                        # 非阻塞方式放入队列
                        self.image_queue.put_nowait(image.copy())
                        self.performance_monitor.update_fps()
                    except queue.Full:
                        # 队列满时丢弃最老的帧
                        try:
                            self.image_queue.get_nowait()
                            self.image_queue.put_nowait(image.copy())
                        except queue.Empty:
                            pass

            except Exception as e:
                logging.error(f"采集帧时出错: {e}")
                self.handle_camera_error(f"采集异常: {e}")
                break
            finally:
                if stOutFrame.pBufAddr is not None:
                    self.cam.MV_CC_FreeImageBuffer(stOutFrame)

        logging.info("采集线程结束")

    def convert_image_data(self, stOutFrame) -> Optional[np.ndarray]:
        """转换图像数据"""
        try:
            pData = cast(stOutFrame.pBufAddr, POINTER(c_ubyte * stOutFrame.stFrameInfo.nFrameLen))
            np_data = np.frombuffer(pData.contents, dtype=np.uint8)

            nWidth = stOutFrame.stFrameInfo.nWidth
            nHeight = stOutFrame.stFrameInfo.nHeight
            pixel_format = stOutFrame.stFrameInfo.enPixelType

            if pixel_format == PixelType_Gvsp_BGR8_Packed:
                image = np_data.reshape(nHeight, nWidth, 3)
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            elif pixel_format == PixelType_Gvsp_RGB8_Packed:
                image = np_data.reshape(nHeight, nWidth, 3)
            elif pixel_format in [PixelType_Gvsp_BayerRG8, PixelType_Gvsp_BayerGB8,
                                  PixelType_Gvsp_BayerGR8, PixelType_Gvsp_BayerBG8]:
                image = np_data.reshape(nHeight, nWidth)
                image = cv2.cvtColor(image, cv2.COLOR_BAYER_RG2RGB)
            else:
                logging.warning(f"不支持的像素格式: {pixel_format}")
                return None

            return image

        except Exception as e:
            logging.error(f"转换图像数据失败: {e}")
            return None

    def process_images(self):
        """图像处理线程 - 优化版"""
        logging.info("处理线程启动")
        
        while self.running:
            try:
                # 从队列获取图像
                image = self.image_queue.get(timeout=1.0)
                
                # 提交到线程池处理
                future = self.detection_executor.submit(self.detect_objects, image)
                
                # 等待结果并发送信号
                try:
                    annotated_image, detection_time = future.result(timeout=5.0)
                    self.update_image_signal.emit(annotated_image)
                    self.performance_monitor.add_detection_time(detection_time)
                    
                    # 更新性能显示
                    self.update_performance_signal.emit(
                        self.performance_monitor.current_fps,
                        self.performance_monitor.get_avg_detection_time()
                    )
                    
                except Exception as e:
                    logging.error(f"检测处理失败: {e}")
                
                # 定期垃圾回收
                self.frame_count += 1
                if self.frame_count % self.config.gc_interval == 0:
                    import gc
                    gc.collect()
                    
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"处理图像时出错: {e}")

        logging.info("处理线程结束")

    def detect_objects(self, image: np.ndarray) -> Tuple[np.ndarray, float]:
        """目标检测 - 优化版"""
        start_time = time.time()
        
        try:
            # YOLO推理
            results = self.model.predict(
                image, 
                conf=self.config.confidence_threshold, 
                verbose=False,
                imgsz=self.config.yolo_input_size
            )
            
            # 绘制检测结果
            annotated_image = self.annotate_image(image, results)
            
            detection_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            return annotated_image, detection_time
            
        except Exception as e:
            logging.error(f"目标检测失败: {e}")
            return image, 0.0

    def annotate_image(self, image: np.ndarray, results) -> np.ndarray:
        """图像标注 - 优化版"""
        try:
            annotated_frame = results[0].plot()
            
            # 遍历检测结果
            for det in results[0]:
                if det.boxes:
                    for box in det.boxes:
                        # 获取边界框信息
                        x1, y1, x2, y2 = map(int, box.xyxy[0])
                        label = det.names[int(box.cls[0])]
                        confidence = float(box.conf[0])
                        
                        # 计算中心点
                        cx, cy = (x1 + x2) // 2, (y1 + y2) // 2
                        
                        # 记录检测信息
                        logging.debug(f"检测: {label}, 置信度: {confidence:.2f}, 中心: ({cx}, {cy})")
                        
                        # 绘制中心点
                        cv2.circle(annotated_frame, (cx, cy), 5, (0, 0, 255), -1)
                        
                        # 绘制坐标信息
                        self.draw_coordinates(annotated_frame, x1, y1, x2, y2, cx, cy)
            
            return annotated_frame
            
        except Exception as e:
            logging.error(f"图像标注失败: {e}")
            return image

    def draw_coordinates(self, image, x1, y1, x2, y2, cx, cy):
        """绘制坐标信息"""
        h, w = image.shape[:2]
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 2
        
        # 中心点坐标
        center_text = f"({cx}, {cy})"
        cv2.putText(image, center_text, (cx + 5, cy + 5), font, font_scale, (0, 0, 255), thickness)
        
        # 四个角点坐标
        coordinates = [
            (f"LT({x1}, {y1})", max(x1 + 10, 10), max(y1 + 30, 30)),
            (f"RT({x2}, {y1})", min(x2 - 100, w - 100), max(y1 - 10, 30)),
            (f"LB({x1}, {y2})", max(x1 + 10, 10), min(y2 + 30, h - 10)),
            (f"RB({x2}, {y2})", min(x2 - 100, w - 100), min(y2 + 30, h - 10))
        ]
        
        for text, text_x, text_y in coordinates:
            cv2.putText(image, text, (text_x, text_y), font, font_scale, (0, 255, 0), thickness)

    @pyqtSlot(np.ndarray)
    def display_image(self, image):
        """显示图像 - 优化版"""
        try:
            h, w, ch = image.shape
            bytes_per_line = ch * w
            q_img = QImage(image.data, w, h, bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_img)

            # 缩放图像以适应标签大小
            scaled_pixmap = pixmap.scaled(
                self.image_label.size(),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )

            self.image_label.setPixmap(scaled_pixmap)
            self.current_image = image
            
        except Exception as e:
            logging.error(f"显示图像失败: {e}")

    @pyqtSlot(float, float)
    def update_performance_display(self, fps, avg_detection_time):
        """更新性能显示"""
        self.fps_label.setText(f"FPS: {fps:.1f}")
        self.detection_time_label.setText(f"检测耗时: {avg_detection_time:.1f}ms")
        self.queue_size_label.setText(f"队列: {self.image_queue.qsize()}/{self.config.max_queue_size}")

    @pyqtSlot(str)
    def update_status_bar(self, message):
        """更新状态栏"""
        self.status_bar.showMessage(message)

    @pyqtSlot(str, str)
    def show_error_message(self, title, message):
        """显示错误消息"""
        QMessageBox.critical(self, title, message)

    def handle_camera_error(self, error_msg):
        """处理相机错误"""
        logging.error(f"相机错误: {error_msg}")
        self.running = False
        QMetaObject.invokeMethod(self, "show_error_message", 
                               Qt.QueuedConnection,
                               Q_ARG(str, "相机错误"),
                               Q_ARG(str, error_msg))

    def save_image(self):
        """保存图像 - 优化版"""
        if self.current_image is None:
            self.show_error_signal.emit("警告", "没有可保存的图像!")
            return

        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"captured_image_{timestamp}.png"
            
            # 使用OpenCV保存图像
            image_bgr = cv2.cvtColor(self.current_image, cv2.COLOR_RGB2BGR)
            success = cv2.imwrite(filename, image_bgr)
            
            if success:
                QMessageBox.information(self, "成功", f"图像已保存为: {filename}")
                logging.info(f"图像保存成功: {filename}")
            else:
                raise Exception("OpenCV保存失败")
                
        except Exception as e:
            error_msg = f"保存图像失败: {str(e)}"
            logging.error(error_msg)
            self.show_error_signal.emit("错误", error_msg)

    def save_config(self):
        """保存配置"""
        try:
            self.config.save_to_file()
            QMessageBox.information(self, "成功", "配置已保存")
            logging.info("配置保存成功")
        except Exception as e:
            error_msg = f"保存配置失败: {str(e)}"
            logging.error(error_msg)
            self.show_error_signal.emit("错误", error_msg)

    def load_config(self):
        """重载配置"""
        try:
            self.config = CameraConfig.load_from_file()
            self.confidence_slider.setValue(int(self.config.confidence_threshold * 100))
            self.confidence_label.setText(f"{self.config.confidence_threshold:.2f}")
            QMessageBox.information(self, "成功", "配置已重载")
            logging.info("配置重载成功")
        except Exception as e:
            error_msg = f"重载配置失败: {str(e)}"
            logging.error(error_msg)
            self.show_error_signal.emit("错误", error_msg)

    def toggle_server(self):
        """切换服务器状态"""
        if self.server_running:
            self.stop_server()
        else:
            self.start_server()

    def start_server(self):
        """启动服务器"""
        try:
            self.server_thread = threading.Thread(target=self.run_server)
            self.server_thread.daemon = True
            self.server_running = True
            self.server_thread.start()
            
            self.server_btn.setText("停止服务器")
            self.server_status_label.setText("服务器状态: 运行中")
            self.update_status_signal.emit("服务器已启动")
            logging.info("服务器启动")
            
        except Exception as e:
            error_msg = f"启动服务器失败: {str(e)}"
            logging.error(error_msg)
            self.show_error_signal.emit("错误", error_msg)

    def stop_server(self):
        """停止服务器"""
        self.server_running = False
        self.server_btn.setText("启动服务器")
        self.server_status_label.setText("服务器状态: 停止")
        self.update_status_signal.emit("服务器已停止")
        logging.info("服务器停止")

    def run_server(self):
        """运行服务器 - 优化版"""
        server_socket = None
        try:
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.settimeout(1.0)  # 设置超时以便可以检查停止条件
            server_socket.bind((self.config.server_ip, self.config.server_port))
            server_socket.listen(5)

            logging.info(f"服务器启动在 {self.config.server_ip}:{self.config.server_port}")

            while self.server_running:
                try:
                    conn, addr = server_socket.accept()
                    logging.info(f"客户端连接: {addr}")
                    
                    # 为每个客户端创建处理线程
                    client_thread = threading.Thread(
                        target=self.handle_client, 
                        args=(conn, addr)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.server_running:
                        logging.error(f"接受连接错误: {e}")
                    break
                    
        except Exception as e:
            logging.error(f"服务器错误: {e}")
        finally:
            if server_socket:
                server_socket.close()
            logging.info("服务器已关闭")

    def handle_client(self, conn, addr):
        """处理客户端连接"""
        try:
            conn.settimeout(5.0)
            while self.server_running:
                try:
                    data = conn.recv(1024)
                    if not data:
                        break
                    
                    # Echo服务器 - 将接收到的数据发送回去
                    response = f"Echo: {data.decode('utf-8', errors='ignore')}"
                    conn.send(response.encode('utf-8'))
                    
                except socket.timeout:
                    continue
                except Exception as e:
                    logging.error(f"客户端 {addr} 通信错误: {e}")
                    break
                    
        except Exception as e:
            logging.error(f"处理客户端 {addr} 时出错: {e}")
        finally:
            try:
                conn.close()
                logging.info(f"客户端 {addr} 连接已关闭")
            except:
                pass

    def closeEvent(self, event):
        """关闭事件处理"""
        logging.info("应用程序正在关闭...")
        
        # 停止采集
        if self.running:
            self.stop_capture()

        # 停止服务器
        if self.server_running:
            self.stop_server()

        # 关闭相机
        if self.cam is not None:
            try:
                self.cam.MV_CC_StopGrabbing()
                self.cam.MV_CC_CloseDevice()
                self.cam.MV_CC_DestroyHandle()
                MvCamera.MV_CC_Finalize()
                logging.info("相机资源已释放")
            except Exception as e:
                logging.error(f"释放相机资源时出错: {e}")

        # 关闭线程池
        self.detection_executor.shutdown(wait=True)
        
        # 保存配置
        try:
            self.config.save_to_file()
        except:
            pass

        logging.info("应用程序已关闭")
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("千歌机器人视觉系统")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("千歌机器人")
    
    try:
        window = CameraWindow()
        window.show()
        
        # 设置异常处理
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            logging.critical("未捕获的异常", exc_info=(exc_type, exc_value, exc_traceback))
            QMessageBox.critical(None, "严重错误", f"程序遇到未处理的异常:\n{exc_value}")
        
        sys.excepthook = handle_exception
        
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"应用程序启动失败: {e}")
        QMessageBox.critical(None, "启动错误", f"应用程序启动失败:\n{e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
