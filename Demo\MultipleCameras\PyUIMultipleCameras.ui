<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1185</width>
    <height>818</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout_2">
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,1">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <layout class="QGridLayout" name="gridLayout">
          <item row="1" column="0">
           <widget class="QWidget" name="widget_display1" native="true">
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>0</height>
             </size>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QCheckBox" name="checkBox_1">
            <property name="text">
             <string>Cam1</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QCheckBox" name="checkBox_3">
            <property name="text">
             <string>Cam3</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QCheckBox" name="checkBox_2">
            <property name="text">
             <string>Cam2</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QCheckBox" name="checkBox_4">
            <property name="text">
             <string>Cam4</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QWidget" name="widget_display2" native="true"/>
          </item>
          <item row="3" column="1">
           <widget class="QWidget" name="widget_display4" native="true"/>
          </item>
          <item row="3" column="0">
           <widget class="QWidget" name="widget_display3" native="true"/>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QTextEdit" name="textEdit">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>120</height>
           </size>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout_2" columnstretch="1,0">
        <property name="leftMargin">
         <number>10</number>
        </property>
        <property name="rightMargin">
         <number>10</number>
        </property>
        <property name="horizontalSpacing">
         <number>30</number>
        </property>
        <property name="verticalSpacing">
         <number>2</number>
        </property>
        <item row="4" column="1">
         <widget class="QPushButton" name="pushButton_triggerOnce">
          <property name="text">
           <string>Trigger by 
software once</string>
          </property>
         </widget>
        </item>
        <item row="6" column="0">
         <widget class="QLabel" name="label_exposure">
          <property name="text">
           <string>Exposure time</string>
          </property>
         </widget>
        </item>
        <item row="6" column="1">
         <widget class="QLineEdit" name="lineEdit_exposureTime">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QRadioButton" name="radioButton_continuous">
          <property name="text">
           <string>Continuous</string>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QPushButton" name="pushButton_startGrab">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="text">
           <string>Start</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QPushButton" name="pushButton_enum">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="text">
           <string>Enum</string>
          </property>
         </widget>
        </item>
        <item row="7" column="0">
         <widget class="QLabel" name="label_gain">
          <property name="text">
           <string>Gain</string>
          </property>
         </widget>
        </item>
        <item row="9" column="1">
         <widget class="QPushButton" name="pushButton_setParams">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="text">
           <string>Set parameter</string>
          </property>
         </widget>
        </item>
        <item row="7" column="1">
         <widget class="QLineEdit" name="lineEdit_gain">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="8" column="1">
         <widget class="QLineEdit" name="lineEdit_frameRate">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QRadioButton" name="radioButton_trigger">
          <property name="text">
           <string>Trigger Mode</string>
          </property>
         </widget>
        </item>
        <item row="5" column="1">
         <widget class="QPushButton" name="pushButton_saveImg">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="text">
           <string>Save image</string>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QPushButton" name="pushButton_stopGrab">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="text">
           <string>Stop</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QPushButton" name="pushButton_close">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="text">
           <string>Close</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QPushButton" name="pushButton_open">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="text">
           <string>Open</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QCheckBox" name="checkBox_software_trigger">
          <property name="text">
           <string>Software 
Trigger</string>
          </property>
         </widget>
        </item>
        <item row="8" column="0">
         <widget class="QLabel" name="label_frameRate">
          <property name="text">
           <string>Frame rate</string>
          </property>
         </widget>
        </item>
        <item row="10" column="0">
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>250</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1185</width>
     <height>26</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
