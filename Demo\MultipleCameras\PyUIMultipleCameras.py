# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'PyUIMultipleCameras.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1185, 818)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.centralwidget)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setObjectName("verticalLayout")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.widget_display1 = QtWidgets.QWidget(self.centralwidget)
        self.widget_display1.setMinimumSize(QtCore.QSize(200, 0))
        self.widget_display1.setObjectName("widget_display1")
        self.gridLayout.addWidget(self.widget_display1, 1, 0, 1, 1)
        self.checkBox_1 = QtWidgets.QCheckBox(self.centralwidget)
        self.checkBox_1.setObjectName("checkBox_1")
        self.gridLayout.addWidget(self.checkBox_1, 0, 0, 1, 1)
        self.checkBox_3 = QtWidgets.QCheckBox(self.centralwidget)
        self.checkBox_3.setObjectName("checkBox_3")
        self.gridLayout.addWidget(self.checkBox_3, 2, 0, 1, 1)
        self.checkBox_2 = QtWidgets.QCheckBox(self.centralwidget)
        self.checkBox_2.setObjectName("checkBox_2")
        self.gridLayout.addWidget(self.checkBox_2, 0, 1, 1, 1)
        self.checkBox_4 = QtWidgets.QCheckBox(self.centralwidget)
        self.checkBox_4.setObjectName("checkBox_4")
        self.gridLayout.addWidget(self.checkBox_4, 2, 1, 1, 1)
        self.widget_display2 = QtWidgets.QWidget(self.centralwidget)
        self.widget_display2.setObjectName("widget_display2")
        self.gridLayout.addWidget(self.widget_display2, 1, 1, 1, 1)
        self.widget_display4 = QtWidgets.QWidget(self.centralwidget)
        self.widget_display4.setObjectName("widget_display4")
        self.gridLayout.addWidget(self.widget_display4, 3, 1, 1, 1)
        self.widget_display3 = QtWidgets.QWidget(self.centralwidget)
        self.widget_display3.setObjectName("widget_display3")
        self.gridLayout.addWidget(self.widget_display3, 3, 0, 1, 1)
        self.verticalLayout.addLayout(self.gridLayout)
        self.textEdit = QtWidgets.QTextEdit(self.centralwidget)
        self.textEdit.setMaximumSize(QtCore.QSize(16777215, 120))
        self.textEdit.setObjectName("textEdit")
        self.verticalLayout.addWidget(self.textEdit)
        self.horizontalLayout.addLayout(self.verticalLayout)
        self.gridLayout_2 = QtWidgets.QGridLayout()
        self.gridLayout_2.setContentsMargins(10, -1, 10, -1)
        self.gridLayout_2.setHorizontalSpacing(30)
        self.gridLayout_2.setVerticalSpacing(2)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.pushButton_triggerOnce = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_triggerOnce.setObjectName("pushButton_triggerOnce")
        self.gridLayout_2.addWidget(self.pushButton_triggerOnce, 4, 1, 1, 1)
        self.label_exposure = QtWidgets.QLabel(self.centralwidget)
        self.label_exposure.setObjectName("label_exposure")
        self.gridLayout_2.addWidget(self.label_exposure, 6, 0, 1, 1)
        self.lineEdit_exposureTime = QtWidgets.QLineEdit(self.centralwidget)
        self.lineEdit_exposureTime.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.lineEdit_exposureTime.setObjectName("lineEdit_exposureTime")
        self.gridLayout_2.addWidget(self.lineEdit_exposureTime, 6, 1, 1, 1)
        self.radioButton_continuous = QtWidgets.QRadioButton(self.centralwidget)
        self.radioButton_continuous.setObjectName("radioButton_continuous")
        self.gridLayout_2.addWidget(self.radioButton_continuous, 2, 0, 1, 1)
        self.pushButton_startGrab = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_startGrab.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_startGrab.setObjectName("pushButton_startGrab")
        self.gridLayout_2.addWidget(self.pushButton_startGrab, 3, 0, 1, 1)
        self.pushButton_enum = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_enum.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_enum.setObjectName("pushButton_enum")
        self.gridLayout_2.addWidget(self.pushButton_enum, 1, 1, 1, 1)
        self.label_gain = QtWidgets.QLabel(self.centralwidget)
        self.label_gain.setObjectName("label_gain")
        self.gridLayout_2.addWidget(self.label_gain, 7, 0, 1, 1)
        self.pushButton_setParams = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_setParams.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_setParams.setObjectName("pushButton_setParams")
        self.gridLayout_2.addWidget(self.pushButton_setParams, 9, 1, 1, 1)
        self.lineEdit_gain = QtWidgets.QLineEdit(self.centralwidget)
        self.lineEdit_gain.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.lineEdit_gain.setObjectName("lineEdit_gain")
        self.gridLayout_2.addWidget(self.lineEdit_gain, 7, 1, 1, 1)
        self.lineEdit_frameRate = QtWidgets.QLineEdit(self.centralwidget)
        self.lineEdit_frameRate.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.lineEdit_frameRate.setObjectName("lineEdit_frameRate")
        self.gridLayout_2.addWidget(self.lineEdit_frameRate, 8, 1, 1, 1)
        self.radioButton_trigger = QtWidgets.QRadioButton(self.centralwidget)
        self.radioButton_trigger.setObjectName("radioButton_trigger")
        self.gridLayout_2.addWidget(self.radioButton_trigger, 2, 1, 1, 1)
        self.pushButton_saveImg = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_saveImg.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_saveImg.setObjectName("pushButton_saveImg")
        self.gridLayout_2.addWidget(self.pushButton_saveImg, 5, 1, 1, 1)
        self.pushButton_stopGrab = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_stopGrab.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_stopGrab.setObjectName("pushButton_stopGrab")
        self.gridLayout_2.addWidget(self.pushButton_stopGrab, 3, 1, 1, 1)
        self.pushButton_close = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_close.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_close.setObjectName("pushButton_close")
        self.gridLayout_2.addWidget(self.pushButton_close, 0, 1, 1, 1)
        self.pushButton_open = QtWidgets.QPushButton(self.centralwidget)
        self.pushButton_open.setMinimumSize(QtCore.QSize(0, 35))
        self.pushButton_open.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_open.setObjectName("pushButton_open")
        self.gridLayout_2.addWidget(self.pushButton_open, 0, 0, 1, 1)
        self.checkBox_software_trigger = QtWidgets.QCheckBox(self.centralwidget)
        self.checkBox_software_trigger.setObjectName("checkBox_software_trigger")
        self.gridLayout_2.addWidget(self.checkBox_software_trigger, 4, 0, 1, 1)
        self.label_frameRate = QtWidgets.QLabel(self.centralwidget)
        self.label_frameRate.setObjectName("label_frameRate")
        self.gridLayout_2.addWidget(self.label_frameRate, 8, 0, 1, 1)
        spacerItem = QtWidgets.QSpacerItem(20, 250, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        self.gridLayout_2.addItem(spacerItem, 10, 0, 1, 1)
        self.gridLayout_2.setColumnStretch(0, 1)
        self.horizontalLayout.addLayout(self.gridLayout_2)
        self.horizontalLayout.setStretch(0, 2)
        self.horizontalLayout.setStretch(1, 1)
        self.horizontalLayout_2.addLayout(self.horizontalLayout)
        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 1185, 26))
        self.menubar.setObjectName("menubar")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "MainWindow"))
        self.checkBox_1.setText(_translate("MainWindow", "Cam1"))
        self.checkBox_3.setText(_translate("MainWindow", "Cam3"))
        self.checkBox_2.setText(_translate("MainWindow", "Cam2"))
        self.checkBox_4.setText(_translate("MainWindow", "Cam4"))
        self.pushButton_triggerOnce.setText(_translate("MainWindow", "Trigger by \n"
"software once"))
        self.label_exposure.setText(_translate("MainWindow", "Exposure time"))
        self.radioButton_continuous.setText(_translate("MainWindow", "Continuous"))
        self.pushButton_startGrab.setText(_translate("MainWindow", "Start"))
        self.pushButton_enum.setText(_translate("MainWindow", "Enum"))
        self.label_gain.setText(_translate("MainWindow", "Gain"))
        self.pushButton_setParams.setText(_translate("MainWindow", "Set parameter"))
        self.radioButton_trigger.setText(_translate("MainWindow", "Trigger Mode"))
        self.pushButton_saveImg.setText(_translate("MainWindow", "Save image"))
        self.pushButton_stopGrab.setText(_translate("MainWindow", "Stop"))
        self.pushButton_close.setText(_translate("MainWindow", "Close"))
        self.pushButton_open.setText(_translate("MainWindow", "Open"))
        self.checkBox_software_trigger.setText(_translate("MainWindow", "Software \n"
"Trigger"))
        self.label_frameRate.setText(_translate("MainWindow", "Frame rate"))
