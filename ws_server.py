# ws_server.py  (新文件)
import asyncio, websockets, json, logging


class ResultBroadcaster:
    def __init__(self, host="0.0.0.0", port=8765):
        self.queue: asyncio.Queue = asyncio.Queue()
        self._server = websockets.serve(self._handler, host, port)
        logging.info(f"WebSocket server listen on ws://{host}:{port}")

    async def _handler(self, ws):
        logging.info("Client connected")
        try:
            while True:
                data = await self.queue.get()
                await ws.send(json.dumps(data))
        except websockets.exceptions.ConnectionClosedError:
            logging.warning("Client disconnected")

    async def run_forever(self):
        async with self._server:
            await asyncio.Future()  # run forever
